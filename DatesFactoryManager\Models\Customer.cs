using System.ComponentModel.DataAnnotations;

namespace DatesFactoryManager.Models
{
    public class Customer : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(15)]
        public string? Phone { get; set; }
        
        [StringLength(100)]
        public string? Email { get; set; }
        
        [StringLength(200)]
        public string? Address { get; set; }
        
        [StringLength(50)]
        public string? City { get; set; }
        
        [StringLength(50)]
        public string? Country { get; set; } = "السعودية";
        
        public CustomerType Type { get; set; } = CustomerType.عادي;
        
        [Range(0, 100)]
        public decimal DiscountPercentage { get; set; } = 0;
        
        public decimal CreditLimit { get; set; } = 0;
        
        public decimal CurrentBalance { get; set; } = 0;
        
        public bool IsActive { get; set; } = true;
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        // Navigation properties
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
    }
    
    public enum CustomerType
    {
        عادي = 1,
        جملة = 2,
        مميز = 3,
        موزع = 4
    }
}
